package com.ets.delivery.application.app.job;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.ets.delivery.application.app.business.AlarmBusiness;
import com.ets.delivery.application.app.business.LogisticsBusiness;
import com.ets.delivery.application.app.factory.storage.StorageFactory;
import com.ets.delivery.application.app.thirdservice.business.WorkWeChatBusiness;
import com.ets.delivery.application.common.bo.LogisticsAvgBO;
import com.ets.delivery.application.common.config.WeChatRobotConfig;
import com.ets.delivery.application.common.consts.StockAlarmConstant;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.utils.ExcelColumnMergeStrategy;
import com.ets.delivery.application.common.vo.InventoryQueryVO;
import com.ets.delivery.application.common.vo.alarm.*;
import com.ets.delivery.application.infra.entity.SupplyGoods;
import com.ets.delivery.application.infra.service.SupplyGoodsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class StockAlarmJob {

    @Value("${spring.profiles.active}")
    private String ACTIVE;

    @Autowired
    private WeChatRobotConfig weChatRobotConfig;

    @Autowired
    private AlarmBusiness alarmBusiness;

    @Autowired
    private LogisticsBusiness logisticsBusiness;

    @Autowired
    private WorkWeChatBusiness workWeChatBusiness;

    @Autowired
    private SupplyGoodsService supplyGoodsService;

    @XxlJob("logisticsAvgHandler")
    public ReturnT<String> logisticsAvgHandler(String params) {
        LogisticsAvgBO logisticsAvgBO = JSON.parseObject(params, LogisticsAvgBO.class);
        if (!StorageCodeEnum.list.contains(logisticsAvgBO.getStorageCode())) {
            log.error("{}仓库不支持", logisticsAvgBO.getStorageCode());
            return ReturnT.FAIL;
        }

        if (logisticsAvgBO.getAvgDay() <= 0) {
            log.error("平均天数必须大于0");
            return ReturnT.FAIL;
        }

        // 获取商品
        List<SupplyGoods> goodsList = supplyGoodsService.getStorageGoodsList(logisticsAvgBO.getStorageCode());
        List<String> goodsCodeList = goodsList.stream().map(SupplyGoods::getGoodsCode).collect(Collectors.toList());
        log.info("【库存预警定时任务】【平均发货量】仓储：{}，商品数量：{}，商品编号列表：{}",
                logisticsAvgBO.getStorageCode(), goodsCodeList.size(), JSON.toJSON(goodsCodeList));

        // 手动创建线程池
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNamePrefix("logistics-average-%d").build();

        ExecutorService poor = new ThreadPoolExecutor(StockAlarmConstant.POOR_MIN_NUM,
                StockAlarmConstant.POOR_MAX_NUM, 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(StockAlarmConstant.CACHE_SIZE),
                threadFactory, new ThreadPoolExecutor.AbortPolicy());

        goodsCodeList.forEach(goodsCode -> poor.execute(() -> {
            // 统计平均发货量
            double quantity = logisticsBusiness.getLogisticsAverage(goodsCode, logisticsAvgBO.getAvgDay(), logisticsAvgBO.getBeforeDay());

            // 设置缓存
            alarmBusiness.setLogisticsAverage(goodsCode, logisticsAvgBO.getBeforeDay(), logisticsAvgBO.getAvgDay(), quantity);
            log.info("【库存预警定时任务】【平均发货量】仓储：{}，商品编码：{}，最近{}天平均发货量{}",
                    logisticsAvgBO.getStorageCode(), goodsCode, logisticsAvgBO.getAvgDay(), quantity);
        }));

        // 线程池关闭
        poor.shutdown();
        return ReturnT.SUCCESS;
    }

    @XxlJob("goodsStockHandler")
    public ReturnT<String> goodsStockHandler(String storageCode) {
        if (!StorageCodeEnum.list.contains(storageCode)) {
            log.error("{}仓库不支持", storageCode);
            return ReturnT.FAIL;
        }

        // 获取商品
        List<SupplyGoods> goodsList = supplyGoodsService.getStorageGoodsList(storageCode);
        List<String> goodsCodeList = goodsList.stream().map(SupplyGoods::getGoodsCode).collect(Collectors.toList());
        log.info("【库存预警定时任务】【库存】仓储：{}，商品数量：{}，商品编号列表：{}",
                storageCode, goodsCodeList.size(), JSON.toJSON(goodsCodeList));
        if (Arrays.asList("dev", "test").contains(ACTIVE)) {
            goodsCodeList = Collections.singletonList("GD001");
        }

        try {
            // 批量获取库存
            List<InventoryQueryVO> inventoryQueryList = StorageFactory.create(storageCode).inventoryQuery(goodsCodeList);
            log.info("【库存预警定时任务】【库存】仓储：{}，商品库存数量：{}，商品库存列表：{}",
                    storageCode, inventoryQueryList.size(), JSON.toJSON(inventoryQueryList));

            LocalDate yesterday = LocalDate.now().minusDays(1);
            if (ObjectUtil.isNotNull(inventoryQueryList)) {
                // 入库记录
                alarmBusiness.setGoodsDailyStock(storageCode, yesterday, inventoryQueryList);
            }
        } catch (Throwable e) {
            log.error("【库存预警定时任务】获取库存错误：{}", e.getLocalizedMessage());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("storageAlarmFileHandler")
    public ReturnT<String> storageAlarmFileHandler(String storageCodeStr) {
        String[] storageCodeArr = storageCodeStr.split(",");

        List<StockAlarmExcelVO> stockAlarmList = new ArrayList<>();
        List<LogisticsAlarmExcelVO> logisticsAlarmList = new ArrayList<>();
        List<YundaDailyLogisticsAlarmExcelVO> yundaStockAlarmList = new ArrayList<>();
        List<JdCloudStockAlarmExcelVO> jdCloudStockAlarmList = new ArrayList<>();
        for(String storageCode : storageCodeArr) {
            if (!StorageCodeEnum.list.contains(storageCode)) {
                log.error("{}仓库不支持", storageCode);
                continue;
            }

            // 获取仓库sku
            List<SupplyGoods> goodsList = supplyGoodsService.getStorageGoodsList(storageCode);
            if (ObjectUtils.isEmpty(goodsList)) {
                continue;
            }

            StorageStockAlarmFileVO storageStockAlarmFileVO = StorageFactory.create(storageCode).stockAlarm(goodsList);
            // 统计韵达仓库存
            if (storageCode.equals(StorageCodeEnum.YUNDA.getValue())) {
                stockAlarmList = storageStockAlarmFileVO.getStockAlarmList();
                logisticsAlarmList = storageStockAlarmFileVO.getLogisticsAlarmList();
                yundaStockAlarmList = storageStockAlarmFileVO.getYundaStockAlarmList();
            }

            // 统计京东仓库存
            if (storageCode.equals(StorageCodeEnum.JD_CLOUD.getValue())) {
                jdCloudStockAlarmList = storageStockAlarmFileVO.getJdCloudStockAlarmList();
            }
        }

        // 排序
        stockAlarmList.sort(Comparator.comparing(StockAlarmExcelVO::getSevenDayLogisticsNum).reversed());
        logisticsAlarmList.sort(Comparator.comparing(LogisticsAlarmExcelVO::getYesterdayLogisticsNum).reversed());
        yundaStockAlarmList.sort(Comparator.comparing(YundaDailyLogisticsAlarmExcelVO::getProfitAndLoss).reversed());

        // 生产文件
        File file = this.createFile(stockAlarmList, logisticsAlarmList, yundaStockAlarmList, jdCloudStockAlarmList);

        // 发送机器人消息
        try {
            workWeChatBusiness.sendFile(file, weChatRobotConfig.getStorageAlarmKey());
        } catch (Throwable e) {
            log.error("【库存预警】发送文件失败：{}", e.getLocalizedMessage());
            return ReturnT.FAIL;
        } finally {
            file.delete();
        }

        try {
            // 生成markdown
            List<String> markdownList = this.createMarkdown(stockAlarmList);
            if (ObjectUtils.isNotEmpty(markdownList)) {
                markdownList.forEach(markdown -> workWeChatBusiness.sendMarkdown(markdown, weChatRobotConfig.getStorageAlarmKey()));
            }
        } catch (Exception e) {
            log.error("【库存预警】发送markdown失败：{}", e.getMessage());
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    @XxlJob("stockOutSkuHandler")
    public ReturnT<String> stockOutSkuHandler(String params) {
        try {
            // 解析天数参数，默认为30天
            int days = 30;
            if (StringUtils.isNotEmpty(params)) {
                try {
                    days = Integer.parseInt(params);
                } catch (NumberFormatException e) {
                    log.error("【缺货SKU统计】参数格式错误：{}", params);
                    return ReturnT.FAIL;
                }
            }
            
            List<Map.Entry<String, Map<String, Object>>> sortedSkuList = logisticsBusiness.getStockOutSkuStats(days);
            
            if (ObjectUtils.isNotEmpty(sortedSkuList)) {
                log.info("【缺货SKU统计】最近{}天缺货SKU统计：{}", days, JSON.toJSONString(sortedSkuList));
                
                // 构建企业微信通知内容
                StringBuilder markdown = new StringBuilder();
                markdown.append("# 缺货SKU统计\n");
                markdown.append("<font color=\"warning\">库存预警通知</font>\n\n");
                markdown.append("> 统计周期：最近").append(days).append("天\n");
                markdown.append("> 缺货SKU总数：<font color=\"warning\">").append(sortedSkuList.size()).append("</font>\n\n");
                
                for (Map.Entry<String, Map<String, Object>> entry : sortedSkuList) {
                    Map<String, Object> stats = entry.getValue();
                    markdown.append("> <font color=\"info\">").append(entry.getKey())
                           .append("</font> | ").append(stats.get("name"))
                           .append(" | 缺货<font color=\"warning\">").append(stats.get("count"))
                           .append("</font>次\n");
                }
                
                // 发送企业微信通知
                try {
                    workWeChatBusiness.sendMarkdown(markdown.toString(), weChatRobotConfig.getStorageAlarmKey());
                } catch (Exception e) {
                    log.error("【缺货SKU统计】发送企业微信通知失败：{}", e.getMessage());
                }
            }
            
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("【缺货SKU统计】统计失败：{}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    public File createFile (List<StockAlarmExcelVO> yundaStockAlarmList,
                             List<LogisticsAlarmExcelVO> yundaLogisticsAlarmList,
                             List<YundaDailyLogisticsAlarmExcelVO> yundaDailyLogisticsAlarmList,
                             List<JdCloudStockAlarmExcelVO> jdCloudStockAlarmList) {
        String basedir = "/data/tmp";
        // 判断文件夹是否存在
        File dir = new File(basedir);
        if (!dir.exists()) {
            if (!dir.mkdirs()) {
                log.error("【库存预警】创建文件夹失败：{}", basedir);
            }
        }
        String fileDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String filename = basedir + File.separator + fileDate + "库存及发货量预警.xlsx";
        File file = new File(filename);

        ExcelWriter excelWriter = EasyExcel.write(file).build();
        WriteSheet stockSheet = EasyExcel.writerSheet(0, "韵达仓库存预警").head(StockAlarmExcelVO.class).build();
        excelWriter.write(yundaStockAlarmList, stockSheet);

        WriteSheet logisticsSheet = EasyExcel.writerSheet(1, "韵达仓发货量异常预警").head(LogisticsAlarmExcelVO.class).build();
        excelWriter.write(yundaLogisticsAlarmList, logisticsSheet);

        WriteSheet yundaStockSheet = EasyExcel.writerSheet(2, "韵达仓商品每日发货监控").head(YundaDailyLogisticsAlarmExcelVO.class).build();
        excelWriter.write(yundaDailyLogisticsAlarmList, yundaStockSheet);

        int[] mergeColumnIndex = {0, 1, 2, 3, 4, 5};
        ExcelColumnMergeStrategy excelColumnMergeStrategy = new ExcelColumnMergeStrategy(mergeColumnIndex, 0);
        WriteSheet jdCloudStockSheet = EasyExcel.writerSheet(3, "京东仓库存预警").registerWriteHandler(excelColumnMergeStrategy).head(JdCloudStockAlarmExcelVO.class).build();
        excelWriter.write(jdCloudStockAlarmList, jdCloudStockSheet);

        excelWriter.finish();
        return file;
    }

    public List<String> createMarkdown(List<StockAlarmExcelVO> yundaStockAlarmList) {
        if (ObjectUtils.isEmpty(yundaStockAlarmList)) {
            log.info("【库存预警】没有库存预警数据");
            return Collections.emptyList();
        }

        // 0<预计可发货天数<=15
        // 近7天有发过货
        List<StockAlarmExcelVO> alarmList = yundaStockAlarmList.stream()
                .filter(stock ->
                        stock.getPredictDayNum() > 0
                                && stock.getPredictDayNum() <= 15
                                && stock.getSevenDayLogisticsNum() > 0).toList();
        if (ObjectUtils.isEmpty(alarmList)) {
            log.info("【库存预警】库存预警没有符合条件数据");
            return Collections.emptyList();
        }

        // 10个商品一组 超过10个就拆开分消息发送
        int groupSize = 10;
        int totalGroups = (int) Math.ceil((double) alarmList.size() / groupSize);
        List<List<StockAlarmExcelVO>> groupedLists = new ArrayList<>();
        for (int i = 0; i < totalGroups; i++) {
            int start = i * groupSize;
            int end = Math.min(start + groupSize, alarmList.size());
            groupedLists.add(alarmList.subList(start, end));
        }

        return groupedLists.stream().map(group -> {
            StringBuilder markdown = new StringBuilder();
            markdown.append("# 库存预警（库存预计可支撑15天）\n");
            markdown.append("<font color=\"warning\">信息仅供参考，实际上库存及发货量预警为准</font>\n\n");
            markdown.append("> 预警SKU总数：<font color=\"warning\">").append(0).append("</font>\n\n");
            alarmList.forEach(stock ->
                    markdown.append("> <font color=\"info\">").append(stock.getGoodsSku())
                            .append("</font>，").append(stock.getGoodsName())
                            .append("</font>，<font color=\"warning\">").append(stock.getPredictDayNum())
                            .append("</font>天\n"));
            return markdown.toString();
        }).collect(Collectors.toList());

    }
}
